import React, { useCallback, useState } from 'react';
import { View } from 'react-native';

import {
  CallToActionBar,
  FiltersModal,
  RecipeGridHeader,
  RecipeGridList,
} from '@features/recipes';
import tw from '@utils/tailwind';

/**
 * TEMP: The current filtering is a temporary solution until the api is configured.
 */
const Recipes = () => {
  const [showFreeRecipesOnly, setShowFreeRecipesOnly] = useState(false);

  const handleShowAllRecipes = useCallback(() => {
    setShowFreeRecipesOnly(false);
  }, []);

  const handleShowFreeRecipes = useCallback(() => {
    setShowFreeRecipesOnly(true);
  }, []);

  return (
    <View style={tw`bg-white flex-1`}>
      <RecipeGridHeader />
      <RecipeGridList showFreeRecipesOnly={showFreeRecipesOnly} />
      <FiltersModal />
      <CallToActionBar
        showFreeRecipesOnly={showFreeRecipesOnly}
        onShowAllRecipes={handleShowAllRecipes}
        onShowFreeRecipes={handleShowFreeRecipes}
      />
    </View>
  );
};

export default React.memo(Recipes);
