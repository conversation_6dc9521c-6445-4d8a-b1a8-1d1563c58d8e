import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { Pressable, View } from 'react-native';
import { LockClosedIcon } from 'react-native-heroicons/solid';

import RemoteImage from '@components/RemoteImage';
import { AddToFavouritesButton } from '@features/ui/button';
import tw from '@utils/tailwind';
import { FALLBACK_IMAGE_URI } from '../../../utils/api';
import { useDataStore } from '../../../utils/store';
import Text from '../../ui/shared/Text';
import { Recipe } from '../types';
import RecipeBookRibbons from './recipe-book-ribbons';

type Props = {
  recipe: Recipe;
  isBookLabelVisible?: boolean;
};

export const RecipeGridItemComponent = ({
  recipe,
  isBookLabelVisible = false,
}: Props) => {
  const navigation = useNavigation<any>();
  const ownedBooks = useDataStore(state => state.ownedBooks);

  const { id, title, image, premium: isRecipePremium } = recipe;

  const isPartOfBook = recipe.books && recipe.books.length > 0;

  const renderBookRibbons = () => {
    if (!recipe.books || !isBookLabelVisible) return null;

    return <RecipeBookRibbons books={recipe.books} />;
  };

  const isUserPremium = useDataStore(state => state.isUserPremium);

  const shouldShowPremiumOverlay = isRecipePremium && !isUserPremium;

  const showPremiumModal = useDataStore(
    state => state.setShouldShowPremiumModal,
  );

  async function goToRecipe() {
    if (isRecipePremium && !isUserPremium) {
      showPremiumModal(true);
      return;
    }

    if (isPartOfBook) {
      if (!recipe.books) return;

      const inaccessibleBooks = recipe.books.filter(
        book => !ownedBooks.includes(book.id),
      );

      if (inaccessibleBooks.length > 0) {
        const book = inaccessibleBooks[0];

        navigation.navigate('Questionnaire', {
          bookId: book.id,
          timeout: book.questionnaire_timeout,
        });

        return;
      }
    }

    navigation.navigate('Recipe', {
      id: recipe.id,
      title: recipe.title,
      premium: recipe.premium,
    });
  }

  return (
    <Pressable onPress={goToRecipe}>
      <View style={tw`bg-white rounded-lg shadow-lg`}>
        <RemoteImage
          uri={image ?? FALLBACK_IMAGE_URI}
          containerStyle={tw`h-[180px]`}
          imageStyle={tw`rounded-t-lg h-[180px]`}
          resizeMode="cover">
          {/* Book Ribbons */}
          {renderBookRibbons()}

          {/* Image Overlay */}
          {shouldShowPremiumOverlay ? (
            <View
              style={tw`w-full h-full bg-slate-500 bg-opacity-50 rounded-t-lg`}>
              <LockClosedIcon
                style={tw`ml-auto mr-3 mt-3`}
                size={25}
                stroke={'#fff'}
                fill={'#fff'}
              />
            </View>
          ) : null}
        </RemoteImage>

        {/* Favourites Button */}
        <View style={tw`absolute right-2 bottom-[80px]`}>
          <AddToFavouritesButton
            premium={isRecipePremium}
            recipeTitle={title}
            recipeId={id}
            bgSize={8}
            iconSize={20}
          />
        </View>

        {/* Text Container */}
        <View
          style={tw`flex-row h-[70px] py-2 px-3 bg-white rounded-b-lg shadow-md justify-between items-center`}>
          <Text
            numberOfLines={3}
            style={tw`self-start flex-1 font-semibold text-[13px] leading-snug text-black mr-2`}>
            {title}
          </Text>
        </View>
      </View>
    </Pressable>
  );
};

export const RecipeGridItem = React.memo(RecipeGridItemComponent);
