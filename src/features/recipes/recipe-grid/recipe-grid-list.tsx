import { useScrollToTop } from '@react-navigation/native';
import { LegendList, LegendListRef } from '@legendapp/list';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { RefreshControl, View, useWindowDimensions } from 'react-native';

import {
  RecipeGridFooter,
  RecipeGridItem,
  Spinner,
  transformInfinitelyPaginatedRecipes,
  useInfiniteRecipes,
} from '@features/recipes';
import ErrorMessage from '@features/ui/shared/ErrorMessage';
import { useRefreshOnFocus } from '@utils/hooks';
import tw from '@utils/tailwind';

const ITEMS_PER_ROW = 2;
const ITEMS_SPACING = 14;
const BOTTOM_NAVIGATION_HEIGHT = 80;
const MIN_FREE_RECIPES = 20;
const EXPECTED_FREE_RATIO = 0.3;

type Props = {
  showFreeRecipesOnly?: boolean;
};

/**
 * TEMP: The current filtering is a temporary solution until the api is configured.
 */
export const RecipeGridList: React.FC<Props> = memo(
  ({ showFreeRecipesOnly }) => {
    const listRef = useRef<LegendListRef>(null);
    const loadingMore = useRef(false);
    const { width } = useWindowDimensions();
    const prevShowFree = useRef(showFreeRecipesOnly);

    const query = useInfiniteRecipes();

    useScrollToTop(listRef);
    useRefreshOnFocus(query.refetch);

    const itemSize = useMemo(() => {
      const availableWidth = width - ITEMS_SPACING * (ITEMS_PER_ROW + 1);
      return availableWidth / ITEMS_PER_ROW;
    }, [width]);

    const allRecipes = useMemo(
      () => transformInfinitelyPaginatedRecipes(query.data?.pages || []),
      [query.data],
    );

    const recipes = useMemo(
      () =>
        showFreeRecipesOnly
          ? allRecipes.filter(
              recipe => !recipe.premium && !recipe.books?.length,
            )
          : allRecipes,
      [allRecipes, showFreeRecipesOnly],
    );

    const loadMore = useCallback(() => {
      if (
        !query.hasNextPage ||
        query.isFetchingNextPage ||
        loadingMore.current
      ) {
        return;
      }
      loadingMore.current = true;
      query.fetchNextPage().finally(() => {
        loadingMore.current = false;
      });
    }, [query]);

    // Handle scroll position reset and initial load when switching views
    useEffect(() => {
      if (prevShowFree.current !== showFreeRecipesOnly) {
        listRef.current?.scrollToOffset({ offset: 0, animated: false });
        if (showFreeRecipesOnly) loadMore();
      }
      prevShowFree.current = showFreeRecipesOnly;
    }, [showFreeRecipesOnly, loadMore]);

    // Auto-load more when showing free recipes and count is low
    useEffect(() => {
      if (
        !showFreeRecipesOnly ||
        loadingMore.current ||
        query.isFetchingNextPage ||
        !query.hasNextPage
      ) {
        return;
      }

      const freeCount = recipes.length;
      const totalCount = allRecipes.length;
      const shouldLoadMore =
        freeCount < MIN_FREE_RECIPES ||
        freeCount / totalCount >= EXPECTED_FREE_RATIO;
      if (shouldLoadMore) loadMore();
    }, [
      showFreeRecipesOnly,
      recipes.length,
      allRecipes.length,
      query.hasNextPage,
      query.isFetchingNextPage,
      loadMore,
    ]);

    const renderGridFooter = () =>
      query.hasNextPage ? (
        query.isFetchingNextPage ? (
          <Spinner />
        ) : null
      ) : (
        <RecipeGridFooter />
      );

    if (query.isError) {
      return <ErrorMessage />;
    }

    if (query.data) {
      return (
        <View style={tw`flex-1`}>
          <LegendList
            ref={listRef}
            key={`grid-${itemSize}`}
            keyExtractor={item => item.id.toString()}
            data={recipes}
            numColumns={ITEMS_PER_ROW}
            showsVerticalScrollIndicator={false}
            renderItem={({ item: recipe }) => (
              <View style={tw`flex-1 px-[11px] mb-[15px]`}>
                <RecipeGridItem recipe={recipe} isBookLabelVisible />
              </View>
            )}
            estimatedItemSize={itemSize}
            refreshControl={
              <RefreshControl
                refreshing={query.isLoading}
                onRefresh={query.refetch}
              />
            }
            onEndReached={loadMore}
            onEndReachedThreshold={0.4}
            contentContainerStyle={tw`pb-[${BOTTOM_NAVIGATION_HEIGHT}px]`}
            ListFooterComponent={renderGridFooter}
          />
        </View>
      );
    }

    return (
      <View style={tw`flex-1 items-center justify-center bg-white`}>
        <Spinner />
      </View>
    );
  },
);
