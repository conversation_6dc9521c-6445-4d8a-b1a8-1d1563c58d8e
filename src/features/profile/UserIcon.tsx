import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { UserCircleIcon } from 'react-native-heroicons/solid';
import { theme } from '../../../tailwind.config';
import tw from '../../utils/tailwind';

const UserIcon = () => {
  const navigation = useNavigation<any>();

  return (
    <TouchableOpacity
      onPress={() => navigation.navigate('Settings')}
      style={tw` rounded`}>
      <UserCircleIcon size={32} color={theme.extend.colors.secondary} />
    </TouchableOpacity>
  );
};

export default UserIcon;
