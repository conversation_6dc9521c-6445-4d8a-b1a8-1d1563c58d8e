import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import {
  ArrowRightOnRectangleIcon,
  ChevronLeftIcon,
} from 'react-native-heroicons/outline';

import { navigationRef } from '@utils/navigation';
import { theme } from '../../tailwind.config';
import HeaderTitle from '../features/ui/shared/HeaderTitle';
import Book from '../screens/Book';
import Challenge from '../screens/Challenge';
import ChallengeItem from '../screens/ChallengeItem';
import Questionnaire from '../screens/questionnaire';
import Recipe from '../screens/Recipe';
import ChangePassword from '../screens/Settings/ChangePassword';
import Favourites from '../screens/Settings/Favourites';
import Profile from '../screens/Settings/Profile';
import Settings from '../screens/Settings/Settings';
import { logout } from '../utils/auth-actions';
import AppDashboardNavigator from './AppDashboardNavigator';

const Stack = createNativeStackNavigator();

const settingsScreenOptons = {
  headerShown: true,
  headerBackTitleVisible: false,
  headerTintColor: theme.extend.colors.black,
};

const LoggedInStackNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        headerTitleAlign: 'center' as const,
      }}>
      <Stack.Screen name="AppDashboard" component={AppDashboardNavigator} />

      <Stack.Screen
        name="Recipe"
        component={Recipe}
        options={({ route }) => ({
          headerTitle: props => (
            <HeaderTitle>
              {props.children === 'Recipe' ? null : props.children}
            </HeaderTitle>
          ),
          headerShown: true,
          headerBackTitleVisible: false,
          headerTintColor: theme.extend.colors.black,
          headerLeft: () => {
            // Only handle deep links
            if (!route.path) return undefined;

            const handleNavigation = () => {
              if (!navigationRef.isReady?.()) return;

              if (navigationRef.canGoBack()) {
                navigationRef.goBack();
              } else {
                navigationRef.reset({
                  index: 0,
                  routes: [{ name: 'AppDashboard' as never }],
                });
              }
            };

            return (
              <TouchableOpacity
                onPress={handleNavigation}
                style={{ marginLeft: -8, marginBottom: 4 }}>
                <ChevronLeftIcon
                  size={28}
                  strokeWidth={2}
                  color={theme.extend.colors.black}
                />
              </TouchableOpacity>
            );
          },
        })}
      />

      <Stack.Screen
        name="Challenge"
        component={Challenge}
        options={{
          headerShown: true,
          title: '',
          headerBackTitleVisible: false,
          headerTransparent: true,
          headerTintColor: theme.extend.colors.black,
        }}
      />

      <Stack.Screen
        name="ChallengeItem"
        component={ChallengeItem}
        options={{
          headerShown: true,
          headerTitle: props => (
            <HeaderTitle>
              {props.children === 'Recipe' ? null : props.children}
            </HeaderTitle>
          ),
          headerBackTitleVisible: false,
          headerTintColor: theme.extend.colors.black,
        }}
      />

      <Stack.Screen
        name="Settings"
        component={Settings}
        options={{
          headerTitle: () => <HeaderTitle>Настройки</HeaderTitle>,
          ...settingsScreenOptons,
          headerRight: () => (
            <TouchableOpacity onPress={() => logout()}>
              <ArrowRightOnRectangleIcon
                size={26}
                strokeWidth={2}
                stroke={theme.extend.colors.black}
              />
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen
        name="Profile"
        component={Profile}
        options={{
          headerTitle: () => <HeaderTitle>Профил</HeaderTitle>,
          ...settingsScreenOptons,
        }}
      />
      <Stack.Screen
        name="Favourites"
        component={Favourites}
        options={{
          headerTitle: () => <HeaderTitle>Любими</HeaderTitle>,
          ...settingsScreenOptons,
        }}
      />
      <Stack.Screen
        name="ChangePassword"
        component={ChangePassword}
        options={{
          headerTitle: () => <HeaderTitle>Смяна на паролата</HeaderTitle>,
          ...settingsScreenOptons,
        }}
      />

      <Stack.Screen
        name="Book"
        component={Book}
        options={{
          // presentation: 'modal',
          headerShown: true,
          title: '',
          headerBackTitleVisible: false,
          headerTransparent: true,
          headerTintColor: theme.extend.colors.black,
        }}
      />

      <Stack.Screen
        name="Questionnaire"
        component={Questionnaire}
        options={{
          presentation: 'modal',
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default LoggedInStackNavigator;
