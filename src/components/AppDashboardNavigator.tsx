import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React from 'react';
import {
  HeartIcon,
  HomeIcon,
  ListBulletIcon,
  MagnifyingGlassIcon,
  ShoppingBagIcon,
  SparklesIcon,
} from 'react-native-heroicons/outline';

import { theme } from '../../tailwind.config';
import DeleteItemsFromShoppingList from '../features/shopping-list/DeleteItemsFromShoppingList';
import HeaderTitle from '../features/ui/shared/HeaderTitle';
import Challenges from '../screens/Challenges';
import Dashboard from '../screens/Dashboard';
import Recipes from '../screens/Recipes';
import Favourites from '../screens/Settings/Favourites';
import Shop from '../screens/Shop';
import { ShoppingList } from '../screens/ShoppingList';

const Tab = createBottomTabNavigator();

const AppDashboardNavigator = () => {
  const iconColor = React.useCallback(
    (focused: boolean) => (focused ? theme.extend.colors.primary : 'gray'),
    [],
  );

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarShowLabel: false,
        headerShown: false,
        headerTitleAlign: 'center' as const,
      }}>
      <Tab.Screen
        name="Dashboard"
        component={Dashboard}
        options={{
          tabBarIcon: ({ focused }) => <HomeIcon color={iconColor(focused)} />,
        }}
      />
      <Tab.Screen
        name="Recipes"
        component={Recipes}
        options={{
          headerShown: true,
          headerShadowVisible: false,
          headerTitle: () => <HeaderTitle>Всички рецепти</HeaderTitle>,
          tabBarIcon: ({ focused }) => (
            <MagnifyingGlassIcon color={iconColor(focused)} />
          ),
        }}
      />
      <Tab.Screen
        name="Shop"
        component={Shop}
        options={{
          headerShown: true,
          headerTitle: () => <HeaderTitle>Магазин</HeaderTitle>,
          tabBarIcon: ({ focused }) => (
            <ShoppingBagIcon color={iconColor(focused)} />
          ),
        }}
      />
      <Tab.Screen
        name="Challenges"
        component={Challenges}
        options={{
          headerShown: true,
          headerTitle: () => <HeaderTitle>Хранителни режими</HeaderTitle>,
          tabBarIcon: ({ focused }) => (
            <SparklesIcon color={iconColor(focused)} />
          ),
        }}
      />
      <Tab.Screen
        name="Favourites"
        component={Favourites}
        options={{
          headerShown: true,
          headerShadowVisible: false,
          headerTitle: () => <HeaderTitle>Любими</HeaderTitle>,
          tabBarIcon: ({ focused }) => <HeartIcon color={iconColor(focused)} />,
        }}
      />
      <Tab.Screen
        name="ShoppingList"
        component={ShoppingList}
        options={{
          headerShown: true,
          headerShadowVisible: true,
          headerTitle: () => <HeaderTitle>Списък за пазаруване</HeaderTitle>,
          tabBarIcon: ({ focused }) => (
            <ListBulletIcon color={iconColor(focused)} />
          ),
          headerRight: () => <DeleteItemsFromShoppingList />,
        }}
      />
    </Tab.Navigator>
  );
};

export default AppDashboardNavigator;
