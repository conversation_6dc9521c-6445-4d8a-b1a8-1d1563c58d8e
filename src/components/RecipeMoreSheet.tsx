import React from 'react';
import {
  Modal,
  Platform,
  Pressable,
  Share,
  TouchableOpacity,
  View,
} from 'react-native';
import { DocumentDuplicateIcon } from 'react-native-heroicons/outline';

import { showToast } from '@utils/in-app-notifications';
import { AddToFavouritesButton } from '../features/ui/button/add-to-favourites-button';
import Container from '../features/ui/shared/Container';
import Heading from '../features/ui/shared/Heading';
import Text from '../features/ui/shared/Text';
import tw from '../utils/tailwind';

type Props = {
  visible: boolean;
  onClose: () => void;
  recipeId: string;
  recipeTitle: string;
  isPremium?: boolean;
};

const RecipeMoreSheet: React.FC<Props> = ({
  visible,
  onClose,
  recipeId,
  recipeTitle,
  isPremium = false,
}) => {
  const handleCopyLink = async () => {
    const result = await Share.share({
      message: `https://app.veganna.bg/go?link=/recipe/${recipeId}/${isPremium}`,
    });
    if (result.action === Share.sharedAction) {
      if (result.activityType === 'com.apple.UIKit.activity.CopyToPasteboard') {
        showToast({
          type: 'info',
          description: 'Линкът към рецептата е успешно копиран',
        });
      }
    }
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}>
      <Pressable style={tw`flex-1`} onPress={onClose}>
        <View style={tw`flex-1 justify-end`}>
          <View
            style={[
              tw`bg-white rounded-t-[30px] w-full shadow-lg shadow-black`,
              {
                shadowOffset: { width: 0, height: -2 },
                elevation: 24,
              },
            ]}>
            <Container style={tw`py-6 mb-4`}>
              <View style={tw`flex-row items-center justify-between mb-6`}>
                <Heading style={tw`text-4xl text-primary text-center`}>
                  Добави в любими {Platform.OS === 'ios' ? '→' : ''}
                </Heading>

                <AddToFavouritesButton
                  recipeId={Number(recipeId)}
                  recipeTitle={recipeTitle}
                  premium={!!isPremium}
                  iconSize={30}
                />
              </View>

              <Text style={tw`text-center text-gray-600 mb-6`}>
                Сподели рецептата с приятели
              </Text>

              <TouchableOpacity
                style={tw`flex-row bg-primary w-full py-4 px-8 rounded-lg items-center justify-center mb-4`}
                onPress={handleCopyLink}
                activeOpacity={0.7}>
                <DocumentDuplicateIcon
                  size={24}
                  color="#FFFFFF"
                  style={tw`mr-3`}
                />

                <Text
                  style={tw`text-white text-base font-body-bold text-center`}>
                  Сподели
                </Text>
              </TouchableOpacity>
            </Container>
          </View>
        </View>
      </Pressable>
    </Modal>
  );
};

export default React.memo(RecipeMoreSheet);
