{"name": "vegan<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios --simulator='iPhone 15 Pro Max'", "ios:se": "react-native run-ios --simulator='iPhone SE (3rd generation)'", "start": "react-native start", "lint": "eslint .", "bundle:ios": "npx react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/VegannaApp/main.jsbundle --assets-dest ios && open ./ios/VegannaApp", "push:android": "appcenter codepush release-react -a gopchiltd/Veganna-Android -d Production -m", "push:ios": "appcenter codepush release-react -a gopchiltd/Veganna -d Production -m"}, "dependencies": {"@legendapp/list": "^1.0.14", "@react-native-community/hooks": "^3.0.0", "@react-native-community/netinfo": "^9.4.1", "@react-native-firebase/analytics": "21.6.1", "@react-native-firebase/app": "21.6.1", "@react-native-firebase/remote-config": "21.6.1", "@react-native-masked-view/masked-view": "^0.2.9", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@shopify/flash-list": "1.7.1", "@tanstack/react-query": "^4.35.3", "axios": "^1.5.0", "fraction.js": "^4.3.6", "immer": "^10.0.3", "react": "18.2.0", "react-hook-form": "^7.46.1", "react-native": "0.72.9", "react-native-check-version": "^1.3.0", "react-native-code-push": "^8.1.0", "react-native-device-info": "^10.9.0", "react-native-fbsdk-next": "^13.1.3", "react-native-heroicons": "^3.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.10.2", "react-native-onesignal": "^5.2.0", "react-native-purchases": "^7.0.0", "react-native-reanimated": "^3.5.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.7.2", "react-native-screenguard": "^1.0.4", "react-native-screens": "^3.25.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^13.13.0", "react-native-toast-message": "^2.2.0", "react-native-turbo-image": "^1.22.3", "twrnc": "^3.6.4", "uuid": "^9.0.1", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@babel/runtime": "^7.22.10", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.2", "@types/react": "^18.2.21", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.49.0", "metro-react-native-babel-preset": "0.77.0", "prettier": "^3.0.3", "react-test-renderer": "18.2.0", "typescript": "5.2.2"}, "engines": {"node": ">=16"}, "packageManager": "npm@10.8.2"}